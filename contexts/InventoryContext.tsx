import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { InventoryItem, InventoryCategory } from '@/components/types';
import { InventoryService } from '@/services/InventoryService';
import { useAuth } from '@/contexts/AuthContext';

interface InventoryContextType {
  inventoryItems: InventoryItem[];
  categorizedInventory: InventoryCategory[];
  loading: boolean;
  isIngredientAvailable: (ingredientName: string) => boolean;
  refreshInventory: () => Promise<void>;
  updateLocalInventory: (items: InventoryItem[]) => void;
}

const InventoryContext = createContext<InventoryContextType | undefined>(undefined);

export const useInventory = () => {
  const context = useContext(InventoryContext);
  if (!context) {
    throw new Error('useInventory must be used within an InventoryProvider');
  }
  return context;
};

interface InventoryProviderProps {
  children: ReactNode;
}

export const InventoryProvider: React.FC<InventoryProviderProps> = ({ children }) => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [categorizedInventory, setCategorizedInventory] = useState<InventoryCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  // Load inventory when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      refreshInventory();
    } else {
      setInventoryItems([]);
      setCategorizedInventory([]);
    }
  }, [isAuthenticated]);

  // Function to check if an ingredient is available in inventory
  const isIngredientAvailable = (ingredientName: string): boolean => {
    if (!ingredientName || !inventoryItems.length) return false;
    
    const item = inventoryItems.find(
      (item) => item.name.toLowerCase().trim() === ingredientName.toLowerCase().trim()
    );
    
    return item ? item.quantity > 0 : false;
  };

  // Load inventory from Firestore
  const refreshInventory = async () => {
    try {
      setLoading(true);
      const items = await InventoryService.getUserInventory();
      setInventoryItems(items);

      if (items.length > 0) {
        const categories = await InventoryService.categorizeInventory(items);
        setCategorizedInventory(categories);
      } else {
        setCategorizedInventory([]);
      }
    } catch (error) {
      console.error('Error loading inventory:', error);
    } finally {
      setLoading(false);
    }
  };

  // Update local inventory state (for optimistic updates)
  const updateLocalInventory = (updatedItems: InventoryItem[]) => {
    setInventoryItems(updatedItems);
    
    // Re-categorize the updated items
    if (updatedItems.length > 0) {
      InventoryService.categorizeInventory(updatedItems)
        .then(categories => setCategorizedInventory(categories))
        .catch(error => console.error('Error re-categorizing inventory:', error));
    } else {
      setCategorizedInventory([]);
    }
  };

  const value = {
    inventoryItems,
    categorizedInventory,
    loading,
    isIngredientAvailable,
    refreshInventory,
    updateLocalInventory,
  };

  return <InventoryContext.Provider value={value}>{children}</InventoryContext.Provider>;
};
